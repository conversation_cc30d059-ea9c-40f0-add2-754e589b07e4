import json
import pytest
from dataclasses import dataclass
from typing import Any, cast
from pydantic import BaseModel, ValidationError

from core.file import File, FileTransferMethod, FileType
from core.variables.segments import (
    SegmentUnion,
    NoneSegment,
    StringSegment,
    FloatSegment,
    IntegerSegment,
    ObjectSegment,
    FileSegment,
    ArrayAnySegment,
    ArrayStringSegment,
    ArrayNumberSegment,
    ArrayObjectSegment,
    ArrayFileSegment,
    Segment,
)
from core.variables.types import SegmentType


@dataclass
class SerializationTestCase:
    """Test case for segment serialization/deserialization"""

    name: str
    segment: Segment
    expected_dict: dict[str, Any]
    expected_value_type: SegmentType


class TestModel(BaseModel):
    """Test model containing a SegmentUnion for testing"""

    segment: SegmentUnion


def create_test_file() -> File:
    """Helper to create a test File object"""
    return File(
        id="test_file_id",
        tenant_id="test_tenant_id",
        type=FileType.IMAGE,
        transfer_method=FileTransferMethod.REMOTE_URL,
        remote_url="https://example.com/test.png",
        filename="test.png",
        extension=".png",
        mime_type="image/png",
        size=1024,
        storage_key="test_storage_key",
    )


def get_serialization_test_cases() -> list[SerializationTestCase]:
    """Generate test cases for all segment types"""
    test_file = create_test_file()

    return [
        SerializationTestCase(
            name="none_segment",
            segment=NoneSegment(),
            expected_dict={"value_type": "none", "value": None},
            expected_value_type=SegmentType.NONE,
        ),
        SerializationTestCase(
            name="string_segment",
            segment=StringSegment(value="Hello, World!"),
            expected_dict={"value_type": "string", "value": "Hello, World!"},
            expected_value_type=SegmentType.STRING,
        ),
        SerializationTestCase(
            name="string_segment_empty",
            segment=StringSegment(value=""),
            expected_dict={"value_type": "string", "value": ""},
            expected_value_type=SegmentType.STRING,
        ),
        SerializationTestCase(
            name="string_segment_unicode",
            segment=StringSegment(value="Hello 世界! 🌍"),
            expected_dict={"value_type": "string", "value": "Hello 世界! 🌍"},
            expected_value_type=SegmentType.STRING,
        ),
        SerializationTestCase(
            name="integer_segment",
            segment=IntegerSegment(value=42),
            expected_dict={"value_type": "number", "value": 42},
            expected_value_type=SegmentType.NUMBER,
        ),
        SerializationTestCase(
            name="integer_segment_zero",
            segment=IntegerSegment(value=0),
            expected_dict={"value_type": "number", "value": 0},
            expected_value_type=SegmentType.NUMBER,
        ),
        SerializationTestCase(
            name="integer_segment_negative",
            segment=IntegerSegment(value=-123),
            expected_dict={"value_type": "number", "value": -123},
            expected_value_type=SegmentType.NUMBER,
        ),
        SerializationTestCase(
            name="float_segment",
            segment=FloatSegment(value=3.14159),
            expected_dict={"value_type": "number", "value": 3.14159},
            expected_value_type=SegmentType.NUMBER,
        ),
        SerializationTestCase(
            name="float_segment_zero",
            segment=FloatSegment(value=0.0),
            expected_dict={"value_type": "number", "value": 0.0},
            expected_value_type=SegmentType.NUMBER,
        ),
        SerializationTestCase(
            name="object_segment_simple",
            segment=ObjectSegment(value={"key": "value"}),
            expected_dict={"value_type": "object", "value": {"key": "value"}},
            expected_value_type=SegmentType.OBJECT,
        ),
        SerializationTestCase(
            name="object_segment_nested",
            segment=ObjectSegment(value={"nested": {"key": "value"}, "array": [1, 2, 3]}),
            expected_dict={"value_type": "object", "value": {"nested": {"key": "value"}, "array": [1, 2, 3]}},
            expected_value_type=SegmentType.OBJECT,
        ),
        SerializationTestCase(
            name="object_segment_empty",
            segment=ObjectSegment(value={}),
            expected_dict={"value_type": "object", "value": {}},
            expected_value_type=SegmentType.OBJECT,
        ),
        SerializationTestCase(
            name="file_segment",
            segment=FileSegment(value=test_file),
            expected_dict={
                "value_type": "file",
                "value": {
                    "dify_model_identity": "FILE_MODEL_IDENTITY",
                    "id": "test_file_id",
                    "tenant_id": "test_tenant_id",
                    "type": "image",
                    "transfer_method": "remote_url",
                    "remote_url": "https://example.com/test.png",
                    "related_id": None,
                    "filename": "test.png",
                    "extension": ".png",
                    "mime_type": "image/png",
                    "size": 1024,
                },
            },
            expected_value_type=SegmentType.FILE,
        ),
        SerializationTestCase(
            name="array_any_segment",
            segment=ArrayAnySegment(value=[1, "string", {"key": "value"}, None]),
            expected_dict={"value_type": "array[any]", "value": [1, "string", {"key": "value"}, None]},
            expected_value_type=SegmentType.ARRAY_ANY,
        ),
        SerializationTestCase(
            name="array_string_segment",
            segment=ArrayStringSegment(value=["hello", "world", ""]),
            expected_dict={"value_type": "array[string]", "value": ["hello", "world", ""]},
            expected_value_type=SegmentType.ARRAY_STRING,
        ),
        SerializationTestCase(
            name="array_number_segment_int",
            segment=ArrayNumberSegment(value=[1, 2, 3]),
            expected_dict={"value_type": "array[number]", "value": [1, 2, 3]},
            expected_value_type=SegmentType.ARRAY_NUMBER,
        ),
        SerializationTestCase(
            name="array_number_segment_float",
            segment=ArrayNumberSegment(value=[1.1, 2.2, 3.3]),
            expected_dict={"value_type": "array[number]", "value": [1.1, 2.2, 3.3]},
            expected_value_type=SegmentType.ARRAY_NUMBER,
        ),
        SerializationTestCase(
            name="array_number_segment_mixed",
            segment=ArrayNumberSegment(value=[1, 2.5, 3]),
            expected_dict={"value_type": "array[number]", "value": [1, 2.5, 3]},
            expected_value_type=SegmentType.ARRAY_NUMBER,
        ),
        SerializationTestCase(
            name="array_object_segment",
            segment=ArrayObjectSegment(value=[{"a": 1}, {"b": 2}, {}]),
            expected_dict={"value_type": "array[object]", "value": [{"a": 1}, {"b": 2}, {}]},
            expected_value_type=SegmentType.ARRAY_OBJECT,
        ),
        SerializationTestCase(
            name="array_file_segment",
            segment=ArrayFileSegment(value=[test_file]),
            expected_dict={
                "value_type": "array[file]",
                "value": [
                    {
                        "dify_model_identity": "FILE_MODEL_IDENTITY",
                        "id": "test_file_id",
                        "tenant_id": "test_tenant_id",
                        "type": "image",
                        "transfer_method": "remote_url",
                        "remote_url": "https://example.com/test.png",
                        "related_id": None,
                        "filename": "test.png",
                        "extension": ".png",
                        "mime_type": "image/png",
                        "size": 1024,
                    }
                ],
            },
            expected_value_type=SegmentType.ARRAY_FILE,
        ),
    ]


class TestSegmentUnionSerialization:
    """Test class for SegmentUnion serialization and deserialization"""

    @pytest.mark.parametrize("test_case", get_serialization_test_cases(), ids=lambda tc: tc.name)
    def test_segment_serialization(self, test_case: SerializationTestCase):
        """Test that segments can be serialized to the expected dictionary format"""
        # Test direct segment serialization
        serialized = test_case.segment.model_dump()

        # Check value_type
        assert serialized["value_type"] == test_case.expected_value_type.value
        assert test_case.segment.value_type == test_case.expected_value_type

        # For file segments, we need to handle the nested serialization
        if isinstance(test_case.segment, (FileSegment, ArrayFileSegment)):
            # File serialization is more complex, so we'll check the structure
            assert "value" in serialized
            if isinstance(test_case.segment, FileSegment):
                assert isinstance(serialized["value"], dict)
                assert "dify_model_identity" in serialized["value"]
            elif isinstance(test_case.segment, ArrayFileSegment):
                assert isinstance(serialized["value"], list)
                if serialized["value"]:  # if not empty
                    assert isinstance(serialized["value"][0], dict)
                    assert "dify_model_identity" in serialized["value"][0]
        else:
            # For non-file segments, check exact match
            assert serialized["value"] == test_case.expected_dict["value"]

    @pytest.mark.parametrize("test_case", get_serialization_test_cases(), ids=lambda tc: tc.name)
    def test_segment_union_in_model_serialization(self, test_case: SerializationTestCase):
        """Test that SegmentUnion works correctly in a Pydantic model during serialization"""
        model = TestModel(segment=test_case.segment)  # type: ignore
        serialized = model.model_dump()

        assert "segment" in serialized
        assert serialized["segment"]["value_type"] == test_case.expected_value_type.value
        assert isinstance(serialized["segment"]["value"], type(test_case.expected_dict["value"]))

    @pytest.mark.parametrize("test_case", get_serialization_test_cases(), ids=lambda tc: tc.name)
    def test_segment_deserialization(self, test_case: SerializationTestCase):
        """Test that dictionary data can be deserialized back to the correct segment type"""
        # First serialize the segment
        serialized = test_case.segment.model_dump()

        # Then deserialize it back through SegmentUnion
        model = TestModel(segment=serialized)  # type: ignore

        # Check that we got the right type back
        assert type(model.segment) == type(test_case.segment)
        assert model.segment.value_type == test_case.expected_value_type

        # For file segments, check that the file properties match
        if isinstance(test_case.segment, FileSegment) and isinstance(model.segment, FileSegment):
            assert model.segment.value.id == test_case.segment.value.id
            assert model.segment.value.filename == test_case.segment.value.filename
        elif isinstance(test_case.segment, ArrayFileSegment) and isinstance(model.segment, ArrayFileSegment):
            assert len(model.segment.value) == len(test_case.segment.value)
            if model.segment.value:  # if not empty
                assert model.segment.value[0].id == test_case.segment.value[0].id
        else:
            # For non-file segments, check exact value match
            assert model.segment.value == test_case.segment.value

    @pytest.mark.parametrize("test_case", get_serialization_test_cases(), ids=lambda tc: tc.name)
    def test_round_trip_serialization(self, test_case: SerializationTestCase):
        """Test that serialize -> deserialize -> serialize produces consistent results"""
        # Original -> Dict
        first_serialization = test_case.segment.model_dump()

        # Dict -> SegmentUnion -> Dict
        model = TestModel(segment=first_serialization)  # type: ignore
        second_serialization = model.segment.model_dump()

        # Should be identical (except for potential floating point precision)
        assert first_serialization["value_type"] == second_serialization["value_type"]

        # For numeric values, handle potential floating point precision issues
        if isinstance(test_case.segment, FloatSegment | ArrayNumberSegment):
            if isinstance(test_case.segment, FloatSegment):
                assert abs(first_serialization["value"] - second_serialization["value"]) < 1e-10
            else:  # ArrayNumberSegment
                for i, (a, b) in enumerate(zip(first_serialization["value"], second_serialization["value"])):
                    if isinstance(a, float) or isinstance(b, float):
                        assert abs(a - b) < 1e-10, f"Mismatch at index {i}: {a} != {b}"
                    else:
                        assert a == b, f"Mismatch at index {i}: {a} != {b}"
        else:
            assert first_serialization["value"] == second_serialization["value"]


class TestSegmentUnionErrorHandling:
    """Test error handling and edge cases for SegmentUnion"""

    def test_invalid_value_type_deserialization(self):
        """Test that invalid value_type raises appropriate error"""
        invalid_data = {"value_type": "invalid_type", "value": "test"}

        with pytest.raises(ValidationError) as exc_info:
            TestModel(segment=invalid_data)  # type: ignore

        assert "invalid_type" in str(exc_info.value)

    def test_missing_value_type_deserialization(self):
        """Test that missing value_type raises appropriate error"""
        invalid_data = {"value": "test"}

        with pytest.raises(ValidationError):
            TestModel(segment=invalid_data)  # type: ignore

        # Should fail because discriminator can't determine the type

    def test_wrong_value_type_for_segment(self):
        """Test that providing wrong value for a segment type raises error"""
        # Try to create StringSegment with non-string value through dict
        invalid_data = {"value_type": "string", "value": 123}

        with pytest.raises(ValidationError):
            TestModel(segment=invalid_data)  # type: ignore

    def test_discriminator_with_non_dict_input(self):
        """Test that discriminator function handles non-dict input appropriately"""
        from core.variables.segments import _get_discriminator_value

        with pytest.raises(TypeError) as exc_info:
            _get_discriminator_value("not_a_dict")

        assert "invalid type for _get_discriminator_value" in str(exc_info.value)

    def test_json_serialization_deserialization(self):
        """Test JSON string serialization and deserialization"""
        test_cases = [
            StringSegment(value="test"),
            IntegerSegment(value=42),
            FloatSegment(value=3.14),
            ObjectSegment(value={"key": "value"}),
            ArrayStringSegment(value=["a", "b", "c"]),
        ]

        for segment in test_cases:
            # Serialize to JSON string
            model = TestModel(segment=segment)
            json_str = model.model_dump_json()

            # Deserialize from JSON string
            model_from_json = TestModel.model_validate_json(json_str)

            # Check that we got the same type and value back
            assert type(model_from_json.segment) == type(segment)
            assert model_from_json.segment.value_type == segment.value_type

            # For numeric types, handle floating point precision
            if isinstance(segment, FloatSegment):
                assert abs(model_from_json.segment.value - segment.value) < 1e-10
            else:
                assert model_from_json.segment.value == segment.value

    def test_special_float_values(self):
        """Test special float values like infinity and NaN"""
        import math

        # Test positive infinity
        inf_segment = FloatSegment(value=float("inf"))
        model = TestModel(segment=inf_segment)
        serialized = model.model_dump()
        assert serialized["segment"]["value"] == float("inf")

        # Test negative infinity
        neg_inf_segment = FloatSegment(value=float("-inf"))
        model = TestModel(segment=neg_inf_segment)
        serialized = model.model_dump()
        assert serialized["segment"]["value"] == float("-inf")

        # Test NaN - note that NaN != NaN, so we need special handling
        nan_segment = FloatSegment(value=float("nan"))
        model = TestModel(segment=nan_segment)
        serialized = model.model_dump()
        assert math.isnan(serialized["segment"]["value"])

    def test_empty_arrays(self):
        """Test empty array segments"""
        empty_array_cases = [
            ArrayAnySegment(value=[]),
            ArrayStringSegment(value=[]),
            ArrayNumberSegment(value=[]),
            ArrayObjectSegment(value=[]),
            ArrayFileSegment(value=[]),
        ]

        for segment in empty_array_cases:
            model = TestModel(segment=segment)
            serialized = model.model_dump()

            assert serialized["segment"]["value"] == []
            assert serialized["segment"]["value_type"] == segment.value_type.value

            # Test round-trip
            model_from_dict = TestModel(segment=serialized["segment"])
            assert type(model_from_dict.segment) == type(segment)
            assert model_from_dict.segment.value == []

    def test_large_values(self):
        """Test segments with large values"""
        # Large string
        large_string = "x" * 10000
        string_segment = StringSegment(value=large_string)
        model = TestModel(segment=string_segment)
        serialized = model.model_dump()
        assert serialized["segment"]["value"] == large_string

        # Large array
        large_array = list(range(1000))
        array_segment = ArrayNumberSegment(value=large_array)
        model = TestModel(segment=array_segment)
        serialized = model.model_dump()
        assert serialized["segment"]["value"] == large_array

        # Large object
        large_object = {f"key_{i}": f"value_{i}" for i in range(100)}
        object_segment = ObjectSegment(value=large_object)
        model = TestModel(segment=object_segment)
        serialized = model.model_dump()
        assert serialized["segment"]["value"] == large_object

    def test_nested_object_complexity(self):
        """Test deeply nested object structures"""
        nested_object = {
            "level1": {
                "level2": {
                    "level3": {"array": [1, 2, {"nested_in_array": True}], "string": "deep_value", "number": 42.5}
                }
            },
            "top_level_array": [{"item1": "value1"}, {"item2": [1, 2, 3]}, None],
        }

        segment = ObjectSegment(value=nested_object)
        model = TestModel(segment=segment)

        # Test serialization
        serialized = model.model_dump()
        assert serialized["segment"]["value"] == nested_object

        # Test deserialization
        model_from_dict = TestModel(segment=serialized["segment"])
        assert model_from_dict.segment.value == nested_object
