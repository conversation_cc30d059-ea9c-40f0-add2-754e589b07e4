import dataclasses
import sys
from services.account_service import AccountService
from configs import dify_config


@dataclasses.dataclass
class Account:
    id: str


def main():
    dify_config.ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 365 * 10  # 10 years.

    account = Account(id=sys.argv[1])
    token = AccountService.get_account_jwt_token(account)
    print("token: ", flush=True)
    print(token, flush=True)


if __name__ == "__main__":
    main()
